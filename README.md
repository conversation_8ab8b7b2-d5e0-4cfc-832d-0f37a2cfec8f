# ESP32 WiFi Switch Prototype

A configurable WiFi-enabled switch system built on the ESP32 platform using the Arduino framework. This project provides a user-friendly web interface for WiFi configuration and device management.

## Features

- **Dual-Mode WiFi Operation**: Operates in both Access Point (AP) and Station (STA) modes simultaneously
- **Persistent WiFi Configuration**: Stores WiFi credentials in EEPROM for automatic reconnection
- **User-Friendly Web Interface**: 
  - Clean, responsive design with rounded edges and consistent styling
  - Easy network scanning and connection
  - Support for hidden networks
  - Password visibility toggle
  - Separate labeled lists for current and available networks
- **Automatic Recovery**: Reconnects to WiFi if connection is lost
- **Always-On Web Server**: Web interface remains accessible even when connected to a WiFi network
- **Real-Time Status Updates**: Displays current connection status and available networks

## Hardware Requirements

- ESP32 development board
- Power supply (USB or external)
- Optional: Additional components for switch functionality

## Software Dependencies

- Arduino IDE
- ESP32 Arduino Core
- ESP8266WiFi library
- ESP8266WebServer library

## Installation

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/Switch_Prototype.git
   ```

2. Open the project in Arduino IDE

3. Install required libraries through the Arduino Library Manager

4. Select your ESP32 board from the Tools > Board menu

5. Upload the sketch to your ESP32 device

## Usage

### Initial Setup

1. Power on the ESP32 device
2. Connect to the "ESP8266_WiFi_Setup" WiFi network with password "12345678"
3. Open a web browser and navigate to http://***********
4. Use the web interface to scan for and connect to your WiFi network

### Web Interface

The web interface provides the following functionality:

- **Network Scanning**: Scan for available WiFi networks
- **Network Connection**: Connect to visible or hidden WiFi networks
- **Connection Status**: View current connection status and IP address
- **Automatic Redirection**: After successful connection, returns to the main page

### LED Indicators

- **Solid LED**: Connected to WiFi
- **Blinking LED**: In setup mode or attempting to connect

## Project Structure

- `Switch_Prototype.ino`: Main Arduino sketch file
- `WiFiManager.h`: Handles WiFi connections and credential storage
- `WebServerManager.h`: Manages the web server and user interface

## Customization

### Changing the Access Point Settings

To modify the default Access Point name and password, edit the following constants in `Switch_Prototype.ino`:

```cpp
const char *AP_SSID = "ESP8266_WiFi_Setup";
const char *AP_PASSWORD = "12345678";
```

### Modifying the Web Interface

The web interface HTML is defined in the `WebServerManager.h` file. You can customize the appearance by modifying the HTML templates and CSS styles.

## Troubleshooting

- **Cannot Connect to Access Point**: Ensure the device is powered and wait for the access point to initialize (about 5-10 seconds)
- **Web Interface Not Loading**: Verify you're connected to the correct WiFi network and using the correct IP address
- **Failed to Connect to WiFi**: Check that your WiFi credentials are correct and the network is in range
- **Device Keeps Rebooting**: Check power supply; ESP32 requires a stable power source

## Contributing

Contributions to improve the project are welcome. Please feel free to submit a pull request or open an issue.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- ESP32/ESP8266 community for their excellent libraries and documentation
- Arduino community for the framework and support
