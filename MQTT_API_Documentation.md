# ESP32 Switch Device - MQTT API Documentation

## Overview

This document describes the MQTT communication protocol for ESP32 Switch devices. Each device can control multiple relays (switches) and provides both remote control capabilities and physical interaction feedback.

**Important**: All MQTT message payloads use JSON format for structured data exchange.

## Device Information

- **Device Type**: Switch Controller
- **Communication Protocol**: MQTT
- **Base Topic**: `home/switches/`
- **Device Identification**: Each device has a unique ID based on its MAC address
- **Switch Count**: Configurable per device (typically 2-8 switches)

## Connection Details

### MQTT Client Configuration
- **Client ID Format**: `ESP32_[DEVICE_ID]`
- **Keep Alive**: Standard MQTT keep-alive
- **Clean Session**: Yes
- **QoS Level**: 0 (Fire and forget)
- **Retained Messages**: Used for state and availability topics

### Last Will and Testament
- **Topic**: `home/switches/[DEVICE_ID]/available`
- **Message**: `{"status": "offline"}`
- **Retained**: Yes

## Topic Structure

All topics follow the hierarchical structure:
```
home/switches/[DEVICE_ID]/[COMPONENT]/[INDEX]/[PROPERTY]
```

Where:
- `[DEVICE_ID]`: Unique device identifier (12-character hex string)
- `[COMPONENT]`: Component type (switch, button, etc.)
- `[INDEX]`: Zero-based index for multiple components
- `[PROPERTY]`: Specific property (state, set, color, etc.)

## Published Topics (Device → Server)

### Device Status

#### Device Availability
- **Topic**: `home/switches/[DEVICE_ID]/available`
- **Payload**: `{"status": "online"}` | `{"status": "offline"}`
- **Retained**: Yes
- **Description**: Device online/offline status

#### Device Name
- **Topic**: `home/switches/[DEVICE_ID]/name`
- **Payload**: `{"name": "Device Name"}`
- **Retained**: Yes
- **Description**: Current device name

### Switch States

#### Individual Switch State
- **Topic**: `home/switches/[DEVICE_ID]/switch/[INDEX]/state`
- **Payload**: `{"state": "ON"}` | `{"state": "OFF"}`
- **Retained**: Yes
- **Description**: Current state of switch at given index

#### Physical Button Press Events
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/pressed`
- **Payload**: `{"press_type": "SINGLE"}` | `{"press_type": "DOUBLE"}` | `{"press_type": "LONG"}`
- **Retained**: No
- **Description**: Physical button interaction events

### Button RGB Status

#### Current RGB Color
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/rgb/state`
- **Payload**: `{"r": 255, "g": 128, "b": 0}`
- **Retained**: Yes
- **Description**: Current RGB color of button backlight

## Subscribed Topics (Server → Device)

### Switch Control

#### Individual Switch Control
- **Topic**: `home/switches/[DEVICE_ID]/switch/[INDEX]/set`
- **Payload**: `{"command": "ON"}` | `{"command": "OFF"}` | `{"command": "TOGGLE"}`
- **Description**: Control individual switch state

#### All Switches Control
- **Topic**: `home/switches/all`
- **Payload**: `{"command": "ON"}` | `{"command": "OFF"}`
- **Description**: Control all switches on all devices simultaneously

### Device Configuration

#### Device Name Change
- **Topic**: `home/switches/[DEVICE_ID]/name/set`
- **Payload**: `{"name": "New Device Name"}`
- **Description**: Change device display name (max 32 characters)

### Button RGB Control

#### Set RGB Color
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/rgb/set`
- **Payload**: `{"r": 255, "g": 128, "b": 0}`
- **Description**: Set RGB color for button backlight
- **Value Ranges**: r, g, b: 0-255

#### RGB Brightness Control
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/brightness/set`
- **Payload**: `{"brightness": 75}`
- **Description**: Set button backlight brightness (0-100 percentage)

#### RGB Effect Control
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/effect/set`
- **Payload**: `{"effect": "solid"}` | `{"effect": "blink"}` | `{"effect": "fade"}` | `{"effect": "rainbow"}`
- **Description**: Set RGB lighting effect

## Message Examples

### Device Connection Sequence
```
1. Device connects with client ID: ESP32_A1B2C3D4E5F6
2. Device publishes: home/switches/A1B2C3D4E5F6/available → {"status": "online"}
3. Device publishes: home/switches/A1B2C3D4E5F6/name → {"name": "Kitchen Switches"}
4. Device publishes: home/switches/A1B2C3D4E5F6/switch/0/state → {"state": "OFF"}
5. Device publishes: home/switches/A1B2C3D4E5F6/switch/1/state → {"state": "ON"}
```

### Switch Control Example
```
Server sends: home/switches/A1B2C3D4E5F6/switch/0/set → {"command": "ON"}
Device responds: home/switches/A1B2C3D4E5F6/switch/0/state → {"state": "ON"}
```

### Device Name Change Example
```
Server sends: home/switches/A1B2C3D4E5F6/name/set → {"name": "Living Room Lights"}
Device responds: home/switches/A1B2C3D4E5F6/name → {"name": "Living Room Lights"}
```

### RGB Control Example
```
Server sends: home/switches/A1B2C3D4E5F6/button/0/rgb/set → {"r": 255, "g": 0, "b": 0}
Device responds: home/switches/A1B2C3D4E5F6/button/0/rgb/state → {"r": 255, "g": 0, "b": 0}
```

### Physical Button Press Example
```
User presses button physically
Device publishes: home/switches/A1B2C3D4E5F6/button/0/pressed → {"press_type": "SINGLE"}
Device publishes: home/switches/A1B2C3D4E5F6/switch/0/state → {"state": "ON"}
```

### RGB Effect Control Example
```
Server sends: home/switches/A1B2C3D4E5F6/button/0/effect/set → {"effect": "blink"}
Server sends: home/switches/A1B2C3D4E5F6/button/0/brightness/set → {"brightness": 80}
```

---

**Document Version**: 1.0
**Last Updated**: Current Date
**Device Firmware Compatibility**: v1.0+
