# ESP32 Switch Device - MQTT API Documentation

## Overview

This document describes the MQTT communication protocol for ESP32 Switch devices. Each device can control multiple relays (switches) and provides both remote control capabilities and physical interaction feedback.

## Device Information

- **Device Type**: Switch Controller
- **Communication Protocol**: MQTT
- **Base Topic**: `home/switches/`
- **Device Identification**: Each device has a unique ID based on its MAC address
- **Switch Count**: Configurable per device (typically 2-8 switches)

## Connection Details

### MQTT Client Configuration
- **Client ID Format**: `ESP32_[DEVICE_ID]`
- **Keep Alive**: Standard MQTT keep-alive
- **Clean Session**: Yes
- **QoS Level**: 0 (Fire and forget)
- **Retained Messages**: Used for state and availability topics

### Last Will and Testament
- **Topic**: `home/switches/[DEVICE_ID]/available`
- **Message**: `offline`
- **Retained**: Yes

## Topic Structure

All topics follow the hierarchical structure:
```
home/switches/[DEVICE_ID]/[COMPONENT]/[INDEX]/[PROPERTY]
```

Where:
- `[DEVICE_ID]`: Unique device identifier (12-character hex string)
- `[COMPONENT]`: Component type (switch, button, etc.)
- `[INDEX]`: Zero-based index for multiple components
- `[PROPERTY]`: Specific property (state, set, color, etc.)

## Published Topics (Device → Server)

### Device Status

#### Device Availability
- **Topic**: `home/switches/[DEVICE_ID]/available`
- **Payload**: `online` | `offline`
- **Retained**: Yes
- **Description**: Device online/offline status

#### Device Name
- **Topic**: `home/switches/[DEVICE_ID]/name`
- **Payload**: Device name (string)
- **Retained**: Yes
- **Description**: Current device name

### Switch States

#### Individual Switch State
- **Topic**: `home/switches/[DEVICE_ID]/switch/[INDEX]/state`
- **Payload**: `ON` | `OFF`
- **Retained**: Yes
- **Description**: Current state of switch at given index

#### Physical Button Press Events
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/pressed`
- **Payload**: `SINGLE` | `DOUBLE` | `LONG`
- **Retained**: No
- **Description**: Physical button interaction events
- **Status**: 🔄 *Planned Feature*

### Button RGB Status

#### Current RGB Color
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/rgb/state`
- **Payload**: `{"r": 255, "g": 128, "b": 0}` (JSON format)
- **Retained**: Yes
- **Description**: Current RGB color of button backlight
- **Status**: 🔄 *Planned Feature*

## Subscribed Topics (Server → Device)

### Switch Control

#### Individual Switch Control
- **Topic**: `home/switches/[DEVICE_ID]/switch/[INDEX]/set`
- **Payload**: `ON` | `OFF` | `TOGGLE`
- **Description**: Control individual switch state

#### All Switches Control
- **Topic**: `home/switches/all`
- **Payload**: `ON` | `OFF`
- **Description**: Control all switches on all devices simultaneously

### Device Configuration

#### Device Name Change
- **Topic**: `home/switches/[DEVICE_ID]/name/set`
- **Payload**: New device name (string, max 32 characters)
- **Description**: Change device display name

### Button RGB Control

#### Set RGB Color
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/rgb/set`
- **Payload**: `{"r": 255, "g": 128, "b": 0}` (JSON format)
- **Description**: Set RGB color for button backlight
- **Value Ranges**: r, g, b: 0-255
- **Status**: 🔄 *Planned Feature*

#### RGB Brightness Control
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/brightness/set`
- **Payload**: `0-100` (integer percentage)
- **Description**: Set button backlight brightness
- **Status**: 🔄 *Planned Feature*

#### RGB Effect Control
- **Topic**: `home/switches/[DEVICE_ID]/button/[INDEX]/effect/set`
- **Payload**: `solid` | `blink` | `fade` | `rainbow`
- **Description**: Set RGB lighting effect
- **Status**: 🔄 *Planned Feature*

## Message Examples

### Device Connection Sequence
```
1. Device connects with client ID: ESP32_A1B2C3D4E5F6
2. Device publishes: home/switches/A1B2C3D4E5F6/available → "online"
3. Device publishes: home/switches/A1B2C3D4E5F6/name → "Kitchen Switches"
4. Device publishes: home/switches/A1B2C3D4E5F6/switch/0/state → "OFF"
5. Device publishes: home/switches/A1B2C3D4E5F6/switch/1/state → "ON"
```

### Switch Control Example
```
Server sends: home/switches/A1B2C3D4E5F6/switch/0/set → "ON"
Device responds: home/switches/A1B2C3D4E5F6/switch/0/state → "ON"
```

### RGB Control Example (Planned)
```
Server sends: home/switches/A1B2C3D4E5F6/button/0/rgb/set → {"r": 255, "g": 0, "b": 0}
Device responds: home/switches/A1B2C3D4E5F6/button/0/rgb/state → {"r": 255, "g": 0, "b": 0}
```

### Physical Button Press Example (Planned)
```
User presses button physically
Device publishes: home/switches/A1B2C3D4E5F6/button/0/pressed → "SINGLE"
Device publishes: home/switches/A1B2C3D4E5F6/switch/0/state → "ON"
```

## Error Handling

### Invalid Payloads
- Device ignores invalid payloads and logs error to serial console
- No error response is published to MQTT

### Connection Loss
- Device attempts reconnection every 5 seconds
- Last Will message automatically published on unexpected disconnection
- Device republishes all states upon reconnection

## Implementation Status

### ✅ Currently Implemented
- Device availability status
- Device name management
- Individual switch control (ON/OFF/TOGGLE)
- All switches control
- Switch state reporting
- Persistent configuration storage

### 🔄 Planned Features
- Physical button press detection and reporting
- RGB button backlight control
- Button brightness control
- RGB lighting effects
- Advanced button press types (single, double, long press)

## Integration Notes

### Home Assistant Integration
This MQTT structure is compatible with Home Assistant's MQTT discovery protocol. Each switch can be auto-discovered as a separate entity.

### Mobile App Considerations
- Subscribe to button press events for real-time UI updates
- Use retained state messages for initial app state synchronization
- Implement RGB color picker UI for button customization

### Server-Side Implementation
- Monitor device availability for connection status
- Store RGB preferences per device/button for persistence
- Implement button press event logging for user analytics

---

**Document Version**: 1.0  
**Last Updated**: Current Date  
**Device Firmware Compatibility**: v1.0+
